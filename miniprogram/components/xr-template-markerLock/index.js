const xr = wx.getXrFrameSystem();
var markerTransform = require('../../pages/xr-template-markerLock/utils/markerTransform');
const CameraStateManager = require('./camera-state')
let prevPos = null

Component({
  properties: {
    initialVpsTracked: Boolean,
    unfinishedItemsCountRaw: Number,
    transformMatrix: null,
    isUp: <PERSON>olean
  },
  data: {
    loaded: true,
    arReady: false,
    activeAnchorList: [],
    isMarkerTrackerOn: false,
    isArCoordSystemInit: false,
    isTracking: false, // 是否正在实时进行用户位置追踪
    cameraTrackingState: -1
  },
  cameraTrs: null,
  arRawData: null,
  camera: null,
  cameraStateManager: null,
  root: null,
  lifetimes: {
    attached() {
      console.log('data', this.data)
    },
    detached() {
      this.pauseTracking()
      this.stopSpawningCameraMesh()
    }
  },
  methods: {
    handleReady({detail}) {
      this.scene = detail.value;
      this.root = this.scene.getElementById('root'); 
      this.mat = new (wx.getXrFrameSystem().Matrix4)();
    },
    handleARReady: async function({detail}) {
      console.log('arReady', this.scene.ar.arVersion);
      this.setData({ arReady: true })
      this.cameraTrs = this.scene.getElementById('camera').getComponent(xr.Transform)
      this.camera = this.scene.getElementById('camera').getComponent(xr.Camera)
      // this.planeTracker = this.scene.getElementById('plane').getComponent(xr.ARTracker)
      this.arRawData = this.scene.ar.getARRawData()
      this.cameraStateManager = new CameraStateManager()
      this.resumeTracking()
    },
    handleOnTick(deltaTime) {  
      try {
        let worldPos = this.cameraTrs.position
        let worldQuat = this.cameraTrs.quaternion
        let originalPose = {
          position: xr.Vector3.createFromNumber(worldPos.x, worldPos.y, worldPos.z),
          quaternion: xr.Quaternion.fromEulerAngles(xr.Vector3.createFromNumber(this.cameraTrs.rotation.x, this.cameraTrs.rotation.y, this.cameraTrs.rotation.z))
        }
        if (!prevPos) {
          prevPos = xr.Vector3.createFromNumber(worldPos.x, worldPos.y, worldPos.z)
        }
        const currCameraTrackingState = this.cameraStateManager.getCameraState(prevPos, worldPos)
        if (!this.data.isArCoordSystemInit) {
          if (currCameraTrackingState === 1)
          {
            console.log('isArActive: ar init')
            this.setData({
              isArCoordSystemInit: true,
              cameraTrackingState: 1
            })
            this.arSystemTransformMatrix = markerTransform.composeRotationMappingMatrix()
            this.triggerEvent('arInit')
          }
          // console.log("摄像机位姿: position: x: "+this.cameraTrs.position.x+", y: "+this.cameraTrs.position.y+", z: "+this.cameraTrs.position.z+"; rotation: x: "+this.cameraTrs.rotation.x+", y: "+this.cameraTrs.rotation.y+", z: "+this.cameraTrs.rotation.z)
        } else {
          if (this.data.cameraTrackingState === 1 && currCameraTrackingState === 2) {
              this.setData({
                cameraTrackingState: 2
              })
              this.triggerEvent('arLost')
              return
          }
            
          if (this.data.cameraTrackingState === 2 && currCameraTrackingState === 1) {
            this.setData({
              cameraTrackingState: 1
            })
            this.triggerEvent('arTracked')
          }
          // console.log("prevPos: x: "+prevPos.x+", y: "+prevPos.y+", z: "+prevPos.z)
          // console.log("worldPos: x: "+worldPos.x+", y: "+worldPos.y+", z: "+worldPos.z)
          prevPos.setValue(worldPos.x, worldPos.y, worldPos.z)
          let arSystemCorrectedPose = null
          if (this.arSystemTransformMatrix) {
            arSystemCorrectedPose = markerTransform.getTransformedPose(Object.values(this.arSystemTransformMatrix), originalPose)
            this.updateCameraPose(arSystemCorrectedPose)
          }
        this.triggerEvent('originalCameraPoseTick', {
            cameraPos: originalPose.position,
            cameraQuat: originalPose.quaternion,
            arRawData: this.arRawData
          })
          //定位成功后相机位姿不需要再变换
          // if (this.data.initialVpsTracked && this.data.transformMatrix) {
          //   let vpsCorrectedPose = markerTransform.getTransformedPose(Object.values(this.data.transformMatrix), arSystemCorrectedPose)
          //   this.updateCameraPose(vpsCorrectedPose)
          // }
          this.triggerEvent('cameraPoseTick', {
            cameraPos: arSystemCorrectedPose.position,
            cameraQuat: arSystemCorrectedPose.quaternion,
            arRawData: this.arRawData
          })
          //console.log("摄像机位姿"+JSON.stringify(cameraTrs.position))
        }
        // console.log('cameraState: '+currCameraTrackingState)
      } catch(err) {
        console.log('[onTick] error: ', err)
      }
    },
    updateCameraPose(correctedPose) {
      try {
        if (!correctedPose || !correctedPose.position || !correctedPose.quaternion) {
          throw new Error("缺少位置或旋转数据");
        }
        this.cameraTrs.position.set(xr.Vector3.createFromNumber(correctedPose.position.x, correctedPose.position.y, correctedPose.position.z))
        this.cameraTrs.quaternion.set(correctedPose.quaternion)
      } catch(err) {
        console.log('[updateCameraPose]: '+err)
      }
    },
    pauseTracking() {
      if (this.data.isTracking) {
        this.scene.event.clear('tick');
        this.onTickCallback = null;  // 清空回调引用
        this.setData({
          isTracking: false
        })
      }
    },
    resumeTracking() {
      if (!this.data.isTracking) {
        if (!this.cameraTrs) {
          console.error('Cannot find cameraTrs')
          return
        }
        if (!this.arRawData) {
          console.error('Cannot find arRawData')
          return
        }

        this.onTickCallback = deltaTime => this.handleOnTick(deltaTime);
        this.scene.event.add('tick', this.onTickCallback);
        this.setData({
          isTracking: true
        })
      }
    },
    refreshActiveAnchorList(anchorList) {
      try {
        if (!Array.isArray(anchorList)) {
          console.error("anchorList is not an array or is undefined");
          return;
        }
        //const activeAnchorList = []
        anchorList.forEach(marker => {
          if (marker.isActive) {
            this.gltfHandler(marker)
          } else {
            this.hideAnchor(marker.id)
          }
        })
        // console.log('activeAnchorList: '+activeAnchorList.map(anchor => anchor.name))
        // const isMarkerModeOn = anchorList.length > 0
        // this.setData({
        //   isMarkerTrackerOn: isMarkerModeOn,
        //   activeAnchorList: activeAnchorList
        // })
      } catch (err) {
        console.log('[refreshActiveAnchorList] error: ', err)
      }
    },
    getPlaneTrackerTransform() {
      // 从 tracker 获取 Transform 组件
      const planeTrackerTransform = this.scene.getElementById('plane').getComponent(xr.Transform)
      if (planeTrackerTransform) {
        // 获取 Transform 的位置、旋转
        const position = {
          x: planeTrackerTransform.worldPosition.x,
          y: planeTrackerTransform.worldPosition.y,
          z: planeTrackerTransform.worldPosition.z
        }
        const euler = planeTrackerTransform.worldQuaternion.toEulerAngles()
        const rotation = {
          x: euler.x,
          y: euler.y,
          z: euler.z
        };
        // console.log('Position:', position);
        // console.log('Rotation:', rotation);
        return {position,rotation}
      } else {
        console.warn("Transform component not found on tracker.");
        return null
      }
    },
    async loadGLTFSingle(marker) {
      const scene = this.scene;    
      if (!this.root) {
        console.error("Root node not found");
        return;
      }
    
      console.log(`Loading GLTF: ${marker.id}`);

      const lockItemNodeId = `lockItem-${marker.id}`
      let lockItemEle = this.root.getChildByName(lockItemNodeId)
    
      // Disable all existing lock items
      // this.hideExistingAnchors()
    
      if (lockItemEle) {
        console.log('show lockItem: '+lockItemEle.id)
        lockItemEle.getComponent(xr.Transform).setData({
          visible: true
        })
      } else {
        // Create a new lock item element
        lockItemEle = scene.createElement(xr.XRNode, {
          id: lockItemNodeId,
          name: lockItemNodeId,
          position: `${marker.position.x} ${marker.position.y} ${marker.position.z}`,
          rotation: `${marker.rotation.x} ${marker.rotation.y} ${marker.rotation.z}`,
        });

        const mesh = scene.createElement(xr.XRMesh, {
          rotation: "-90 0 0",
          position: "0 0 0",
          scale: `${marker.scale.x*0.21} ${marker.scale.y} ${marker.scale.z*0.297}`,
          geometry: "plane",
          material: "standard-mat",
          uniforms: "u_baseColorMap: planeTexture",
          states: "cullOn: false"
        });

        lockItemEle.addChild(mesh)
        this.root.addChild(lockItemEle)
        console.log(`GLTF asset loaded and added to scene: ${marker.id}`);
        this.setData({ gltfLoaded: true });

        // try {
        //   // Load or fetch GLTF asset
        //   let gltfAsset = await this.loadGLTFAsset(gltfItem);
        //   if (!gltfAsset) {
        //     console.error(`Failed to load GLTF asset for: ${gltfItem.id}`);
        //     return;
        //   }
      
        //   // Add GLTF instances for each world pose
        //   const worldPoses = gltfItem.worldPoses || [];
        //   const gltfElements = worldPoses.map(pose => {
        //     const gltf = scene.createElement(xr.XRGLTF, {
        //       position: pose.position,
        //       scale: pose.scale,
        //       rotation: pose.rotation,
        //       'anim-autoplay': '',
        //     });
      
        //     gltf.getComponent(xr.GLTF).setData({ model: gltfAsset.value });
        //     return gltf;
        //   });
      
        //   // Attach all children at once
        //   gltfElements.forEach(child => lockItemEle.addChild(child));
        //   root.addChild(lockItemEle);
      
        //   console.log(`GLTF asset loaded and added to scene: ${gltfItem.id}`);
        //   this.setData({ gltfLoaded: true });
        // } catch (error) {
        //   console.error(`Error loading GLTF item ${gltfItem.id}:`, error);
        // }
      }
    },
    hideAnchor(anchorId) {
      if (!this.root) {
        console.error("Root node not found");
        return;
      }
      const lockItemNodeId = `lockItem-${anchorId}`
      // Disable all existing lock items
      const lockItemToHide = this.root.getChildByName(lockItemNodeId)
      if (lockItemToHide) {
        console.log('hide other lockItem: '+lockItemNodeId)
        lockItemToHide.getComponent(xr.Transform).setData({
          visible: false
        })
      }
    },
    async gltfHandler(markerInfo) {
      this.setData({
        gltfLoaded: false
      })
      try {
        await this.loadGLTFSingle(markerInfo)
      } catch (err) {
        console.log('[gltf load] error: ', err)
      }
    },
    handleTrackerState({detail}) {
      console.log('handleTrackerState')
      const tracker = detail.value
      // 获取当前状态和错误信息
      const {state, errorMessage} = tracker;
      const markerId = tracker.el.id.substring(7)

      if (state === 2) {
        if (markerId != 'entryMarker' && this.data.initialVpsTracked)
        {
          this.data.activeAnchorList.forEach(async (marker) => {
            if (marker.id === markerId) {
              await this.gltfHandler(marker)
          }
          })
        } else {
          this.recordPlaneTrackerTransform(markerId)
        }
    }
    },
    spawnCameraPoseMesh() {
      const cameraEuler = this.cameraTrs.quaternion.toEulerAngles()
      const cameraPos = this.cameraTrs.position

      const meshNode = this.scene.createElement(xr.XRNode, {
        rotation: `${cameraEuler.x*180/Math.PI} ${cameraEuler.y*180/Math.PI} ${cameraEuler.z*180/Math.PI}`,
        position: `${cameraPos.x} ${cameraPos.y} ${cameraPos.z}`,
      })

      const meshX = this.scene.createElement(xr.XRMesh, {
        position: `0.05 0 0`,
        scale: `0.1 0.02 0.02`,
        geometry: "cube",
        uniforms: "u_baseColorFactor:0.7 0.3 0.3 1"
      });
      const meshY = this.scene.createElement(xr.XRMesh, {
        position: `0 0.05 0`,
        scale: `0.02 0.1 0.02`,
        geometry: "cube",
        uniforms: "u_baseColorFactor:0.3 0.7 0.3 1"
      });
      const meshZ = this.scene.createElement(xr.XRMesh, {
        position: `0 0 0.05`,
        scale: `0.02 0.02 0.1`,
        geometry: "cube",
        uniforms: "u_baseColorFactor:0.3 0.3 0.7 1"
      });
      const root = this.scene.getElementById('root');
      if (!root) {
        console.error('Root element not found');
        return;
      }
      meshNode.addChild(meshX);
      meshNode.addChild(meshY);
      meshNode.addChild(meshZ);
      root.addChild(meshNode)
    },
    stopSpawningCameraMesh() {
      clearInterval(this.data.spawnCameraMeshIntervalId)
      this.setData({
        spawnCameraMeshIntervalId: null
      })
      this.spawnCameraPoseMesh()
    },
    async spawnAnchorItem(marker) {
      if (!marker || !marker.position || !marker.rotation || !marker.scale) {
        console.error('Invalid marker data');
        return;
      }
      const root = this.scene.getElementById('root');
      if (!root) {
        console.error('Root element not found');
        return;
      }

      let meshNode = root.getChildByName('anchor-'+marker.id)
      if (meshNode) {
        const meshNodeTrs = meshNode.getComponent(xr.Transform)
        meshNodeTrs.position.setValue(marker.position.x, marker.position.y, marker.position.z)
        meshNodeTrs.rotation.setValue(marker.rotation.x*Math.PI/180, marker.rotation.y*Math.PI/180, marker.rotation.z*Math.PI/180)
      } else {
        meshNode = this.scene.createElement(xr.XRNode, {
          id: marker.id,
          name: marker.name,
          position: `${marker.position.x} ${marker.position.y} ${marker.position.z}`,
          rotation: `${marker.rotation.x} ${marker.rotation.y} ${marker.rotation.z}`,
        })
      
        const mesh = this.scene.createElement(xr.XRMesh, {
          rotation: "-90 0 0",
          position: "0 0 0",
          scale: `${marker.scale.x*0.21} ${marker.scale.y} ${marker.scale.z*0.297}`,
          geometry: "plane",
          material: "standard-mat",
          uniforms: "u_baseColorMap: planeTexture",
          states: "cullOn: false"
        });

        // // Load or fetch GLTF asset
			  // let gltfAsset = await this.loadGLTFAsset(marker.assetId, marker.url);
			  // if (!gltfAsset) {
				// console.error(`Failed to load GLTF asset for: ${marker.assetId}`);
				// return;
			  // }
		
			  // // Add GLTF instances for each world pose
				// const gltf = this.scene.createElement(xr.XRGLTF, {
				//   name: `gltf-${marker.id}`,
				//   position: `0 0 0`,
				//   rotation: `0 0 180`,
				//   scale: `${marker.scale.x} ${marker.scale.y} ${marker.scale.z}`,
				//   'anim-autoplay': '',
				// });
				
				// gltf.getComponent(xr.GLTF).setData({ model: gltfAsset.value });
  
        meshNode.addChild(mesh)
        root.addChild(meshNode);
      }
    },
    recordPlaneTrackerTransform(markerId) {
      const trackerTrs = this.getPlaneTrackerTransform()
      this.triggerEvent('trackerPositionReceived', {
        id: markerId,
        position: trackerTrs.position,
        rotation: trackerTrs.rotation
      })
    },
    async loadGLTFAsset(assetId, url) {
      const scene = this.scene;
	  
	  if (!scene) {
		  console.log('scene is undefined')
		  return
	  }
      // Check if asset already exists
      let gltfAsset = scene.assets.getAssetWithState('gltf', assetId);
      console.log(assetId+' gltfAsset is null: '+(!gltfAsset.value)+', state: '+gltfAsset.state)
      if (!gltfAsset || !gltfAsset.value) {
        try {
			console.log('loadGLTFAsset id: '+assetId)
		  // Load asset if not found
		  gltfAsset = await scene.assets.loadAsset({
			type: 'gltf',
			assetId,
			src: url,
		  });
		} catch (err) {
		  console.error("Asset loading error:", err);
		  return null;
		}
      }
      return gltfAsset;
    },
  },
})