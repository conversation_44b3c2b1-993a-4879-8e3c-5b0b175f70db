.chat-assistant {
  position: fixed;
  right: 4vh;
  width: 30%;
  background-color: transparent;
  display: flex;
  flex-direction: column;
}

/* 输入框容器 */
.input-container {
  display: flex;
  align-items: center;
  padding: 8px;
  margin: 0 10px 10px 0;
}

/* 输入框样式，宽度占90% */
.message-input {
  flex: 1;
  width: 100%;
  height: 35px;
  padding: 0 10px;
  border: 1px solid rgb(7, 6, 6);
  border-radius: 5px;
  margin: 0 5px 0 5px;
  background: white;
}

/* 发送按钮样式 */
.send-button {
  padding: 0;
  background: transparent;
  border: none;
}

.send-button[disabled] {
  padding: 0 !important;
  background: transparent !important;
  border: none !important;
}

/* 发送按钮图标 */
.send-icon {
  width: 24px;
  height: 24px;
  padding: 12%;
  background: rgb(235, 248, 248);
  border: 1px solid rgb(235, 248, 248);
  border-radius: 50%;
}

.send-icon.disable {
  background: rgb(213, 214, 214);
  border: 1px solid rgb(213, 214, 214);
}

/* 消息窗口样式 */
.messages-window {
  flex: 1;
  padding: 10px;
  overflow-y: auto;
  width: 90%;
  border: 1px solid rgb(7, 6, 6);
  border-radius: 5px;
  margin: 0 10px 20px 20px;
  background: white;
  text-align: start;
}

/* 消息项 */
.message-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 10px;
}

/* 头像样式 */
.avatar {
  width: 32px;
  height: 32px;
  border-radius: 25%;
  margin-right: 8px;
}

/* 用户消息 */
.user-message {
  align-self: flex-end;
  background-color: #007aff;
  color: white;
  padding: 8px;
  border-radius: 5px;
  max-width: 70%;
}

/* 助手消息 */
.assistant-message {
  align-self: flex-start;
  background-color: #e0e0e0;
  color: #333;
  padding: 8px;
  border-radius: 5px;
  max-width: 70%;
}

/* 横屏适配 */
@media (orientation: landscape) {
  .chat-assistant {
    height: 80vh; /* 横屏时增加高度占比 */
    bottom: 5vh; /* 减少底部间距 */
  }

  /* 横屏时输入框容器调整 */
  .input-container {
    padding: 6px 8px; /* 减少内边距 */
    margin: 0 8px 8px 0; /* 减少外边距 */
  }

  /* 横屏时输入框高度调整 */
  .message-input {
    height: 32px; /* 稍微减少高度 */
    padding: 0 8px; /* 减少内边距 */
    margin: 0 4px 0 4px; /* 减少外边距 */
  }

  /* 横屏时发送按钮图标调整 */
  .send-icon {
    width: 22px; /* 稍微减小 */
    height: 22px;
    padding: 10%; /* 减少内边距 */
  }

  /* 横屏时消息窗口调整 */
  .messages-window {
    padding: 8px; /* 减少内边距 */
    margin: 0 8px 15px 8px; /* 减少外边距 */
  }

  /* 横屏时消息项间距调整 */
  .message-item {
    margin-bottom: 8px; /* 减少消息间距 */
  }

  /* 横屏时头像调整 */
  .avatar {
    width: 28px; /* 稍微减小头像 */
    height: 28px;
    margin-right: 6px; /* 减少右边距 */
  }

  /* 横屏时消息气泡调整 */
  .user-message,
  .assistant-message {
    padding: 6px 8px; /* 减少内边距 */
    max-width: 75%; /* 横屏时可以稍微增加最大宽度 */
    font-size: 14px; /* 稍微减小字体 */
    line-height: 1.4; /* 调整行高 */
  }
}
